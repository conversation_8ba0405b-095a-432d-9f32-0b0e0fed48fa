const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testWithSid() {
  console.log('=== Testing API with Session ID (sid) ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    const sid = loginRes.data.data.sid;
    const sn = loginRes.data.data.sn;
    
    console.log(`✓ Login successful`);
    console.log(`  Token: ${token.substring(0, 8)}...`);
    console.log(`  SID: ${sid}`);
    console.log(`  SN: ${sn}`);

    // Extract session cookie
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    // Step 2: Get the HTML page to extract embedded variables
    console.log('\nStep 2: Getting embedded session variables from HTML');
    
    const htmlRes = await axios.get(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/ehr/home_user`, {
      headers: {
        'User-Agent': 'Mozilla/5.0',
        'Cookie': sessionCookie
      }
    });

    // Extract embedded variables from HTML
    const html = htmlRes.data;
    const sidMatch = html.match(/var sid = '([^']+)'/);
    const stokMatch = html.match(/var stok = '([^']+)'/);
    const snMatch = html.match(/var sn = '([^']+)'/);

    const embeddedSid = sidMatch ? sidMatch[1] : null;
    const embeddedStok = stokMatch ? stokMatch[1] : null;
    const embeddedSn = snMatch ? snMatch[1] : null;

    console.log(`Embedded SID: ${embeddedSid}`);
    console.log(`Embedded STOK: ${embeddedStok}`);
    console.log(`Embedded SN: ${embeddedSn}`);

    // Step 3: Test API calls using the embedded session ID
    console.log('\nStep 3: Testing API calls with embedded session variables\n');
    
    const testMethods = [
      'get_clients',
      'get_devices',
      'clients',
      'devices',
      'get_status',
      'status',
      'get_info',
      'info',
      'get_network_status',
      'network_status',
      'get_dhcp_clients',
      'dhcp_clients',
      'get_wireless_clients',
      'wireless_clients',
    ];

    // Test with different session ID approaches
    const sessionApproaches = [
      { name: 'Using embedded SID in params', sid: embeddedSid },
      { name: 'Using login SID in params', sid: sid },
      { name: 'Using embedded SID in URL', sid: embeddedSid, useInUrl: true },
    ];

    for (const approach of sessionApproaches) {
      console.log(`\n=== ${approach.name} ===`);
      
      for (const method of testMethods) {
        try {
          let requestData;
          let requestUrl;
          
          if (approach.useInUrl) {
            // Try using SID in URL instead of token
            requestUrl = `/cgi-bin/luci/;sid=${approach.sid}/api/auth`;
            requestData = { method: method };
          } else {
            // Try using SID in params
            requestUrl = `/cgi-bin/luci/api/auth`;
            requestData = { 
              method: method,
              sid: approach.sid
            };
          }

          const response = await axios.post(`http://${ROUTER_CONFIG.ip}${requestUrl}`, requestData, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0',
              'Cookie': sessionCookie,
            },
            timeout: 5000
          });

          if (response.data && response.data.error && response.data.error.message === "Method not found.") {
            // Skip method not found
            continue;
          }

          console.log(`✓ Method: ${method}`);
          console.log(`  Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
          
          if (response.data && response.data.data !== null && response.data.data !== undefined) {
            console.log(`  🎯 SUCCESS - Got data!`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          }
          
          // Check for device data
          const dataStr = JSON.stringify(response.data).toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
            console.log(`  🔥 CONTAINS DEVICE DATA!`);
          }
          
        } catch (error) {
          if (error.response && error.response.status !== 404) {
            console.log(`❌ Method ${method} - Status: ${error.response.status}`);
          }
        }
      }
    }

    // Step 4: Try alternative API endpoints with session ID
    console.log('\n\nStep 4: Testing alternative endpoints with session ID\n');
    
    const alternativeEndpoints = [
      `/cgi-bin/luci/;sid=${embeddedSid}/api/auth`,
      `/cgi-bin/luci/;sid=${embeddedSid}/api/network`,
      `/cgi-bin/luci/;sid=${embeddedSid}/api/overview`,
      `/cgi-bin/luci/;sid=${embeddedSid}/api/wireless`,
      `/cgi-bin/luci/;sid=${embeddedSid}/api/system`,
      `/cgi-bin/luci/;sid=${embeddedSid}/rpc/network`,
      `/cgi-bin/luci/;sid=${embeddedSid}/rpc/sys`,
    ];

    for (const endpoint of alternativeEndpoints) {
      try {
        console.log(`Testing: ${endpoint}`);
        
        const response = await axios.get(`http://${ROUTER_CONFIG.ip}${endpoint}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0',
            'Cookie': sessionCookie,
            'Accept': 'application/json, text/plain, */*',
          },
          timeout: 5000
        });

        console.log(`  Status: ${response.status}`);
        console.log(`  Content-Type: ${response.headers['content-type'] || 'unknown'}`);
        
        if (response.headers['content-type']?.includes('json')) {
          console.log(`  🎯 JSON Response: ${JSON.stringify(response.data).substring(0, 300)}...`);
          
          if (response.data && response.data.data !== null && response.data.data !== undefined) {
            console.log(`  ✅ SUCCESS - Got real data!`);
          }
        } else if (response.data && response.data.length < 1000) {
          console.log(`  📄 Response: ${response.data.substring(0, 200)}...`);
        } else {
          console.log(`  📄 Response length: ${response.data?.length || 0} bytes`);
          if (response.data && response.data.includes('<div id=app></div>')) {
            console.log(`  ⚠️  SPA HTML shell`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response) {
          console.log(`  ❌ Status: ${error.response.status}`);
        } else {
          console.log(`  ❌ Error: ${error.message}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ SID test failed:', error.message);
  }
}

testWithSid().catch(console.error);
