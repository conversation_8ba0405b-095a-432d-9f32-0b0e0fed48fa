const axios = require('axios');
const fs = require('fs');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function analyzeJsApiCalls() {
  console.log('=== Deep Analysis of JavaScript API Calls ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Download and analyze the main app JavaScript file
    console.log('\nStep 2: Downloading main JavaScript file for detailed analysis');
    
    const appJsUrl = `http://${ROUTER_CONFIG.ip}/luci-static/eweb-ehr/static/js/app15b49cadc29cfe563ebe.js`;
    
    const jsRes = await axios.get(appJsUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0',
        'Accept': 'application/javascript, */*'
      },
      timeout: 30000
    });

    const jsContent = jsRes.data;
    console.log(`Downloaded ${jsContent.length} bytes of JavaScript`);

    // Save to file for analysis
    fs.writeFileSync('app.js', jsContent);
    console.log('Saved JavaScript to app.js for analysis');

    // Step 3: Search for specific API call patterns
    console.log('\nStep 3: Searching for API call patterns in JavaScript\n');

    // Look for axios/fetch calls
    const axiosPatterns = [
      /axios\.[a-z]+\([^)]+\)/gi,
      /\$http\.[a-z]+\([^)]+\)/gi,
      /fetch\([^)]+\)/gi,
      /\$\.ajax\([^)]+\)/gi,
      /\$\.get\([^)]+\)/gi,
      /\$\.post\([^)]+\)/gi,
    ];

    console.log('=== HTTP Request Patterns ===');
    for (const pattern of axiosPatterns) {
      const matches = jsContent.match(pattern) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for ${pattern.source}:`);
        matches.slice(0, 5).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match.substring(0, 100)}...`);
        });
        console.log('');
      }
    }

    // Look for URL construction patterns
    console.log('=== URL Construction Patterns ===');
    const urlPatterns = [
      /['"`][^'"`]*\/api\/[^'"`]*['"`]/gi,
      /['"`][^'"`]*\/cgi-bin\/[^'"`]*['"`]/gi,
      /stok\s*\+[^;,)]+/gi,
      /sid\s*\+[^;,)]+/gi,
      /token\s*\+[^;,)]+/gi,
    ];

    for (const pattern of urlPatterns) {
      const matches = jsContent.match(pattern) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for ${pattern.source}:`);
        matches.slice(0, 10).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match}`);
        });
        console.log('');
      }
    }

    // Look for method names that might be used in API calls
    console.log('=== API Method Patterns ===');
    const methodPatterns = [
      /method\s*:\s*['"`][^'"`]+['"`]/gi,
      /action\s*:\s*['"`][^'"`]+['"`]/gi,
      /'[a-zA-Z_][a-zA-Z0-9_]*'\s*:\s*function/gi,
      /"[a-zA-Z_][a-zA-Z0-9_]*"\s*:\s*function/gi,
    ];

    for (const pattern of methodPatterns) {
      const matches = jsContent.match(pattern) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for ${pattern.source}:`);
        matches.slice(0, 10).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match}`);
        });
        console.log('');
      }
    }

    // Look for specific device/client related functions
    console.log('=== Device/Client Related Functions ===');
    const devicePatterns = [
      /[a-zA-Z_][a-zA-Z0-9_]*[Cc]lient[s]?[a-zA-Z0-9_]*\s*[:=]/gi,
      /[a-zA-Z_][a-zA-Z0-9_]*[Dd]evice[s]?[a-zA-Z0-9_]*\s*[:=]/gi,
      /[a-zA-Z_][a-zA-Z0-9_]*[Dd]hcp[a-zA-Z0-9_]*\s*[:=]/gi,
      /[a-zA-Z_][a-zA-Z0-9_]*[Ww]ireless[a-zA-Z0-9_]*\s*[:=]/gi,
    ];

    for (const pattern of devicePatterns) {
      const matches = jsContent.match(pattern) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for ${pattern.source}:`);
        matches.slice(0, 10).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match}`);
        });
        console.log('');
      }
    }

    // Look for specific strings that might indicate API endpoints
    console.log('=== Potential API Endpoints ===');
    const endpointKeywords = ['client', 'device', 'dhcp', 'wireless', 'network', 'status', 'overview', 'home'];
    
    for (const keyword of endpointKeywords) {
      const regex = new RegExp(`['"\`][^'"\`]*${keyword}[^'"\`]*['"\`]`, 'gi');
      const matches = jsContent.match(regex) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} matches for "${keyword}":`);
        const uniqueMatches = [...new Set(matches)];
        uniqueMatches.slice(0, 10).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match}`);
        });
        console.log('');
      }
    }

    // Look for JSON-RPC style calls
    console.log('=== JSON-RPC Style Calls ===');
    const rpcPatterns = [
      /\{\s*method\s*:\s*['"`][^'"`]+['"`][^}]*\}/gi,
      /\{\s*['"`]method['"`]\s*:\s*['"`][^'"`]+['"`][^}]*\}/gi,
    ];

    for (const pattern of rpcPatterns) {
      const matches = jsContent.match(pattern) || [];
      if (matches.length > 0) {
        console.log(`Found ${matches.length} RPC-style calls:`);
        matches.slice(0, 10).forEach((match, i) => {
          console.log(`  ${i + 1}: ${match.substring(0, 150)}...`);
        });
        console.log('');
      }
    }

    console.log('\n=== Analysis Complete ===');
    console.log('Check app.js file for full JavaScript content');
    console.log('Look for patterns that show how the SPA makes API calls to get device data');

  } catch (error) {
    console.error('❌ JavaScript analysis failed:', error.message);
  }
}

analyzeJsApiCalls().catch(console.error);
