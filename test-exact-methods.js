const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testExactMethods() {
  console.log('=== Testing Exact Methods Found in JavaScript ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Test devSta.get with different parameter formats
    console.log('\nStep 2: Testing devSta.get with different parameter formats\n');
    
    const devStaTests = [
      // No parameters
      { method: "devSta.get" },
      
      // Empty parameters
      { method: "devSta.get", params: {} },
      
      // Different parameter formats
      { method: "devSta.get", params: "neighbor" },
      { method: "devSta.get", params: "esw_neighbor" },
      { method: "devSta.get", params: "client" },
      { method: "devSta.get", params: "device" },
      
      // Array format
      { method: "devSta.get", params: ["neighbor"] },
      { method: "devSta.get", params: ["esw_neighbor"] },
      
      // Different object formats
      { method: "devSta.get", params: { type: "neighbor" } },
      { method: "devSta.get", params: { type: "esw_neighbor" } },
      { method: "devSta.get", params: { name: "neighbor" } },
      { method: "devSta.get", params: { name: "esw_neighbor" } },
      { method: "devSta.get", params: { target: "neighbor" } },
      { method: "devSta.get", params: { target: "esw_neighbor" } },
      
      // Try without module prefix
      { method: "devSta.get", params: { module: "" } },
      { method: "devSta.get", params: { module: null } },
    ];

    for (const test of devStaTests) {
      try {
        console.log(`Testing devSta.get with params: ${JSON.stringify(test.params || 'none')}`);
        
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, test, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 10000
        });

        console.log(`  Status: ${response.status}`);
        
        if (response.data) {
          const responseStr = JSON.stringify(response.data);
          console.log(`  Response: ${responseStr.substring(0, 300)}${responseStr.length > 300 ? '...' : ''}`);
          
          if (response.data.error && response.data.error.message === "Method not found.") {
            console.log(`  ❌ Method not found`);
          } else if (response.data.data !== null && response.data.data !== undefined) {
            console.log(`  🎯 SUCCESS - Got data!`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          } else if (response.data.code === 0 && !response.data.error) {
            console.log(`  ✅ SUCCESS - Method exists (data might be null)`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          }
          
          const dataStr = responseStr.toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168') ||
              dataStr.includes('neighbor') || dataStr.includes('arp') || dataStr.includes('ethernet')) {
            console.log(`  🔥 CONTAINS DEVICE/NETWORK DATA!`);
          }
        }
        console.log('');
        
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`);
      }
    }

    // Step 3: Test other promising methods from the list
    console.log('\nStep 3: Testing other promising methods\n');
    
    const promisingMethods = [
      // User/device list methods
      { method: "user_list.set" },
      { method: "user_list.del" },
      { method: "user_list.user_cnt" },
      
      // Network methods
      { method: "network.set" },
      
      // Wireless methods
      { method: "wireless.set" },
      { method: "wireless.active" },
      
      // Port status
      { method: "port_status.isWanLink" },
      
      // System methods that might return device info
      { method: "portInfo.set" },
      { method: "vlan_port.sta" },
      
      // Methods that might be GET instead of SET
      { method: "user_list.get" },
      { method: "network.get" },
      { method: "wireless.get" },
      { method: "portInfo.get" },
      { method: "vlan_port.get" },
      
      // Try some methods without parameters
      { method: "checkNet" }, // We know this works
    ];

    for (const test of promisingMethods) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, test, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 5000
        });

        if (response.data && response.data.error && response.data.error.message === "Method not found.") {
          // Skip method not found
          continue;
        }

        console.log(`✓ Method: ${test.method}`);
        console.log(`  Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
        
        if (response.data && response.data.data !== null && response.data.data !== undefined) {
          console.log(`  🎯 SUCCESS - Got data!`);
          console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
        } else if (response.data && response.data.code === 0 && !response.data.error) {
          console.log(`  ✅ SUCCESS - Method exists`);
          console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
        }
        
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log(`  🔥 CONTAINS DEVICE DATA!`);
        }
        console.log('');
        
      } catch (error) {
        // Skip errors for this batch test
      }
    }

    // Step 4: Try to find the correct way to call devSta.get by looking at the JavaScript pattern
    console.log('\nStep 4: Testing devSta.get with JavaScript-style parameters\n');
    
    // From the JavaScript, the pattern was: {method:"devSta.get",params:{module:"neighbor"}}
    // But maybe the router expects a different format
    const jsStyleTests = [
      // Try the exact format from JavaScript
      { method: "devSta.get", params: { module: "neighbor" } },
      { method: "devSta.get", params: { module: "esw_neighbor" } },
      
      // Try with different parameter names that might be expected
      { method: "devSta.get", params: { cmd: "neighbor" } },
      { method: "devSta.get", params: { cmd: "esw_neighbor" } },
      { method: "devSta.get", params: { action: "neighbor" } },
      { method: "devSta.get", params: { action: "esw_neighbor" } },
      { method: "devSta.get", params: { request: "neighbor" } },
      { method: "devSta.get", params: { request: "esw_neighbor" } },
      
      // Try with device parameter
      { method: "devSta.get", params: { device: "pc" } },
      { method: "devSta.get", params: { device: "all" } },
      { method: "devSta.get", params: { device: "client" } },
      
      // Try combinations
      { method: "devSta.get", params: { module: "neighbor", device: "pc" } },
      { method: "devSta.get", params: { module: "esw_neighbor", device: "pc" } },
    ];

    for (const test of jsStyleTests) {
      try {
        console.log(`Testing devSta.get with JS-style params: ${JSON.stringify(test.params)}`);
        
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, test, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 10000
        });

        if (response.data && response.data.error && response.data.error.message === "Method not found.") {
          console.log(`  ❌ Method not found`);
          continue;
        }

        console.log(`  Status: ${response.status}`);
        console.log(`  Response: ${JSON.stringify(response.data).substring(0, 300)}...`);
        
        if (response.data && response.data.data !== null && response.data.data !== undefined) {
          console.log(`  🎯 SUCCESS - Got data!`);
          console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
        } else if (response.data && response.data.code === 0 && !response.data.error) {
          console.log(`  ✅ SUCCESS - Method exists`);
          console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
        }
        
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log(`  🔥 CONTAINS DEVICE DATA!`);
        }
        console.log('');
        
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Exact method test failed:', error.message);
  }
}

testExactMethods().catch(console.error);
