const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testApiResponses() {
  console.log('=== Testing API Response Content ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    
    // Extract session cookie
    let sessionCookie = '';
    if (loginRes.headers['set-cookie']) {
      const cookies = loginRes.headers['set-cookie'];
      for (const cookie of cookies) {
        const [nameValue] = cookie.split(';');
        sessionCookie = nameValue;
        break;
      }
    }

    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Test specific API endpoints and show full responses
    console.log('\nStep 2: Testing API endpoints with full response content\n');
    
    const testCases = [
      { 
        name: 'Network API - POST with method',
        url: `/cgi-bin/luci/;stok=${token}/api/network`,
        method: 'POST',
        data: { method: 'get' }
      },
      { 
        name: 'Network API - POST with status method',
        url: `/cgi-bin/luci/;stok=${token}/api/network`,
        method: 'POST',
        data: { method: 'status' }
      },
      { 
        name: 'Network API - POST with clients method',
        url: `/cgi-bin/luci/;stok=${token}/api/network`,
        method: 'POST',
        data: { method: 'clients' }
      },
      { 
        name: 'Overview API - POST with get method',
        url: `/cgi-bin/luci/;stok=${token}/api/overview`,
        method: 'POST',
        data: { method: 'get' }
      },
      { 
        name: 'Wireless API - POST with clients method',
        url: `/cgi-bin/luci/;stok=${token}/api/wireless`,
        method: 'POST',
        data: { method: 'clients' }
      },
      { 
        name: 'System API - POST with status method',
        url: `/cgi-bin/luci/;stok=${token}/api/system`,
        method: 'POST',
        data: { method: 'status' }
      },
      { 
        name: 'Network API - Direct GET',
        url: `/cgi-bin/luci/;stok=${token}/api/network`,
        method: 'GET',
        data: null
      },
      { 
        name: 'Overview API - Direct GET',
        url: `/cgi-bin/luci/;stok=${token}/api/overview`,
        method: 'GET',
        data: null
      },
    ];

    for (const testCase of testCases) {
      try {
        console.log(`\n=== ${testCase.name} ===`);
        
        let response;
        if (testCase.method === 'POST') {
          response = await axios.post(`http://${ROUTER_CONFIG.ip}${testCase.url}`, testCase.data, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0',
              'Cookie': sessionCookie,
              'Accept': 'application/json, text/plain, */*',
              'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: 10000
          });
        } else {
          response = await axios.get(`http://${ROUTER_CONFIG.ip}${testCase.url}`, {
            headers: {
              'User-Agent': 'Mozilla/5.0',
              'Cookie': sessionCookie,
              'Accept': 'application/json, text/plain, */*',
              'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: 10000
          });
        }

        console.log(`Status: ${response.status}`);
        console.log(`Content-Type: ${response.headers['content-type'] || 'unknown'}`);
        console.log(`Content-Length: ${response.data?.length || 0} bytes`);
        
        // Show the actual response content
        if (response.headers['content-type']?.includes('json')) {
          console.log('JSON Response:');
          console.log(JSON.stringify(response.data, null, 2));
        } else if (typeof response.data === 'string') {
          if (response.data.length > 1000) {
            console.log('Large HTML Response (first 500 chars):');
            console.log(response.data.substring(0, 500) + '...');
            
            // Check if it's the SPA HTML
            if (response.data.includes('<div id=app></div>')) {
              console.log('⚠️  This is the SPA HTML shell, not API data');
            }
          } else {
            console.log('Full Response:');
            console.log(response.data);
          }
        } else if (typeof response.data === 'object') {
          console.log('Object Response:');
          console.log(JSON.stringify(response.data, null, 2));
        } else {
          console.log('Unknown Response Type:');
          console.log(response.data);
        }
        
        // Check for device-related content
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log('🔥 CONTAINS DEVICE-RELATED DATA!');
        }
        
      } catch (error) {
        console.log(`❌ ${testCase.name} failed:`);
        if (error.response) {
          console.log(`  Status: ${error.response.status}`);
          console.log(`  Response: ${error.response.data}`);
        } else {
          console.log(`  Error: ${error.message}`);
        }
      }
    }

    // Step 3: Try some alternative approaches based on common router patterns
    console.log('\n\nStep 3: Testing alternative router API patterns\n');
    
    const alternativeTests = [
      {
        name: 'LuCI RPC Call',
        url: `/cgi-bin/luci/rpc/sys`,
        method: 'POST',
        data: { method: 'net.devices' }
      },
      {
        name: 'LuCI RPC Network',
        url: `/cgi-bin/luci/rpc/network`,
        method: 'POST',
        data: { method: 'get_status' }
      },
      {
        name: 'Direct LuCI Call',
        url: `/cgi-bin/luci/admin/network/dhcp`,
        method: 'GET',
        data: null
      },
      {
        name: 'UCI Show',
        url: `/cgi-bin/luci/;stok=${token}/api/cmd`,
        method: 'POST',
        data: { method: 'uci show dhcp' }
      },
      {
        name: 'ARP Table',
        url: `/cgi-bin/luci/;stok=${token}/api/cmd`,
        method: 'POST',
        data: { method: 'cat /proc/net/arp' }
      },
    ];

    for (const test of alternativeTests) {
      try {
        console.log(`\n=== ${test.name} ===`);
        
        let response;
        if (test.method === 'POST') {
          response = await axios.post(`http://${ROUTER_CONFIG.ip}${test.url}`, test.data, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0',
              'Cookie': sessionCookie,
              'Accept': 'application/json, text/plain, */*',
              'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: 10000
          });
        } else {
          response = await axios.get(`http://${ROUTER_CONFIG.ip}${test.url}`, {
            headers: {
              'User-Agent': 'Mozilla/5.0',
              'Cookie': sessionCookie,
              'Accept': 'application/json, text/plain, */*',
              'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: 10000
          });
        }

        console.log(`Status: ${response.status}`);
        console.log(`Content-Type: ${response.headers['content-type'] || 'unknown'}`);
        
        if (response.headers['content-type']?.includes('json')) {
          console.log('JSON Response:');
          console.log(JSON.stringify(response.data, null, 2));
        } else if (typeof response.data === 'string' && response.data.length < 1000) {
          console.log('Response:');
          console.log(response.data);
        } else {
          console.log(`Response length: ${response.data?.length || 0} bytes`);
          if (typeof response.data === 'string') {
            console.log('First 300 chars:');
            console.log(response.data.substring(0, 300) + '...');
          }
        }
        
      } catch (error) {
        console.log(`❌ ${test.name} failed:`);
        if (error.response) {
          console.log(`  Status: ${error.response.status}`);
        } else {
          console.log(`  Error: ${error.message}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ API response test failed:', error.message);
  }
}

testApiResponses().catch(console.error);
