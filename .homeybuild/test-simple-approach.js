const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testSimpleApproach() {
  console.log('=== Testing Simple Approach from Example ===\n');

  try {
    console.log('Step 1: Login');

    // Login using the working format for this router
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    console.log('Login response:', loginRes.status, loginRes.data);
    console.log('Login cookies:', loginRes.headers['set-cookie']);

    if (!loginRes.data || !loginRes.data.data || !loginRes.data.data.token) {
      console.log('❌ Login failed - no token received');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Extract cookies from login response
    const cookies = [];
    if (loginRes.headers['set-cookie']) {
      loginRes.headers['set-cookie'].forEach(cookie => {
        const [nameValue] = cookie.split(';');
        cookies.push(nameValue);
      });
    }
    const cookieString = cookies.join('; ');
    console.log('Extracted cookies:', cookieString);

    console.log('\nStep 2: Access page with token in URL');

    // Access page with token AND cookies
    const homeUserRes = await axios.get(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/ehr/home_user`, {
      headers: {
        'User-Agent': 'Mozilla/5.0',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/web/home`,
        'Cookie': cookieString
      }
    });

    console.log('Home user response:', homeUserRes.status, 'Data length:', homeUserRes.data?.length || 0);

    // Check if we got a login page
    if (homeUserRes.data && typeof homeUserRes.data === 'string') {
      if (homeUserRes.data.includes('loginPass') || homeUserRes.data.includes('Log In') || homeUserRes.data.includes('password')) {
        console.log('❌ Got login page - authentication failed');
        console.log('First 500 chars of response:');
        console.log(homeUserRes.data.substring(0, 500));
      } else {
        console.log('✓ Got authenticated page - success!');
        console.log('Page contains device data:', homeUserRes.data.includes('192.168.') ? 'YES' : 'NO');

        // Look for device indicators
        const deviceIndicators = [
          'MAC', 'mac', 'IP', 'device', 'client', 'wireless', 'ethernet'
        ];

        const foundIndicators = deviceIndicators.filter(indicator =>
          homeUserRes.data.toLowerCase().includes(indicator.toLowerCase())
        );

        console.log('Found device-related terms:', foundIndicators.join(', '));
      }
    }

    console.log('\nStep 3: Try different headers');

    // Try with different headers that might be required
    const headers = [
      {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      {
        'User-Agent': 'Mozilla/5.0',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/`,
        'X-Requested-With': 'XMLHttpRequest'
      },
      {
        'User-Agent': 'Mozilla/5.0',
        'Referer': `http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}`,
        'Accept': 'application/json, text/javascript, */*; q=0.01'
      }
    ];

    for (let i = 0; i < headers.length; i++) {
      try {
        console.log(`\nTrying header set ${i + 1}:`);
        const testRes = await axios.get(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/;stok=${token}/ehr/home_user`, {
          headers: { ...headers[i], 'Cookie': cookieString }
        });

        const isLoginPage = testRes.data && typeof testRes.data === 'string' &&
          (testRes.data.includes('loginPass') || testRes.data.includes('Log In'));

        console.log(`  Status: ${testRes.status}, Login page: ${isLoginPage ? 'YES' : 'NO'}`);

        if (!isLoginPage) {
          console.log('  ✓ Success with this header set!');
          break;
        }
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testSimpleApproach().catch(console.error);
