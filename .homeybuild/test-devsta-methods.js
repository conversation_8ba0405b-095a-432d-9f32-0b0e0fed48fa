const axios = require('axios');

const ROUTER_CONFIG = {
  ip: '*************',
  password: 'pcs2ass2ADM'
};

async function testDevStaMethods() {
  console.log('=== Testing devSta.get Methods for Device Data ===\n');
  
  try {
    // Step 1: Login and get session
    console.log('Step 1: Login');
    const loginRes = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, {
      method: "login",
      params: {
        password: ROUTER_CONFIG.password,
        time: Math.floor(Date.now() / 1000).toString(),
        encry: false,
        limit: false
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0',
      }
    });

    if (!loginRes.data?.data?.token) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginRes.data.data.token;
    console.log(`✓ Login successful - Token: ${token.substring(0, 8)}...`);

    // Step 2: Test the devSta.get methods found in JavaScript
    console.log('\nStep 2: Testing devSta.get methods\n');
    
    const devStaMethods = [
      // Methods found in JavaScript analysis
      { method: "devSta.get", params: { module: "neighbor" } },
      { method: "devSta.get", params: { module: "esw_neighbor" } },
      
      // Additional likely modules
      { method: "devSta.get", params: { module: "client" } },
      { method: "devSta.get", params: { module: "clients" } },
      { method: "devSta.get", params: { module: "device" } },
      { method: "devSta.get", params: { module: "devices" } },
      { method: "devSta.get", params: { module: "dhcp" } },
      { method: "devSta.get", params: { module: "wireless" } },
      { method: "devSta.get", params: { module: "network" } },
      { method: "devSta.get", params: { module: "status" } },
      { method: "devSta.get", params: { module: "overview" } },
      { method: "devSta.get", params: { module: "system" } },
      { method: "devSta.get", params: { module: "interface" } },
      { method: "devSta.get", params: { module: "lan" } },
      { method: "devSta.get", params: { module: "wan" } },
      { method: "devSta.get", params: { module: "arp" } },
      { method: "devSta.get", params: { module: "mac" } },
      { method: "devSta.get", params: { module: "ip" } },
      { method: "devSta.get", params: { module: "connected" } },
      { method: "devSta.get", params: { module: "online" } },
      
      // Alternative method names
      { method: "devSta.list", params: { module: "neighbor" } },
      { method: "devSta.status", params: { module: "neighbor" } },
      { method: "devSta.info", params: { module: "neighbor" } },
      
      // Other potential methods from analysis
      { method: "checkNet" },
      { method: "cmdArr", params: { device: "pc" } },
      { method: "cmdArrEsw" },
    ];

    for (const methodCall of devStaMethods) {
      try {
        console.log(`Testing: ${methodCall.method}${methodCall.params ? ` with params: ${JSON.stringify(methodCall.params)}` : ''}`);
        
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, methodCall, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 10000
        });

        console.log(`  Status: ${response.status}`);
        
        if (response.data) {
          const responseStr = JSON.stringify(response.data);
          console.log(`  Response: ${responseStr.substring(0, 300)}${responseStr.length > 300 ? '...' : ''}`);
          
          // Check if we got a successful response (not "Method not found")
          if (response.data.error && response.data.error.message === "Method not found.") {
            console.log(`  ❌ Method not found`);
          } else if (response.data.data !== null && response.data.data !== undefined) {
            console.log(`  🎯 SUCCESS - Got data!`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          } else if (response.data.code === 0 && !response.data.error) {
            console.log(`  ✅ SUCCESS - Method exists (even if data is null)`);
            console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
          }
          
          // Check for device-related content
          const dataStr = responseStr.toLowerCase();
          if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
              dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168') ||
              dataStr.includes('neighbor') || dataStr.includes('arp') || dataStr.includes('ethernet')) {
            console.log(`  🔥 CONTAINS DEVICE/NETWORK DATA!`);
          }
        }
        console.log('');
        
      } catch (error) {
        if (error.response) {
          console.log(`  ❌ Error - Status: ${error.response.status}`);
        } else {
          console.log(`  ❌ Error: ${error.message}`);
        }
      }
    }

    // Step 3: Test other potential API patterns found in the JavaScript
    console.log('\nStep 3: Testing other API patterns\n');
    
    const otherMethods = [
      // Network related
      { method: "network.get" },
      { method: "network.status" },
      { method: "network.clients" },
      { method: "network.devices" },
      
      // Overview related
      { method: "overview.get" },
      { method: "overview.status" },
      { method: "overview.data" },
      
      // System related
      { method: "system.get" },
      { method: "system.status" },
      { method: "system.info" },
      
      // DHCP related
      { method: "dhcp.get" },
      { method: "dhcp.clients" },
      { method: "dhcp.leases" },
      
      // Wireless related
      { method: "wireless.get" },
      { method: "wireless.clients" },
      { method: "wireless.status" },
      
      // Direct module calls
      { method: "neighbor" },
      { method: "esw_neighbor" },
      { method: "client" },
      { method: "clients" },
      { method: "devices" },
    ];

    for (const methodCall of otherMethods) {
      try {
        const response = await axios.post(`http://${ROUTER_CONFIG.ip}/cgi-bin/luci/api/auth`, methodCall, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0',
          },
          timeout: 5000
        });

        if (response.data && response.data.error && response.data.error.message === "Method not found.") {
          // Skip method not found
          continue;
        }

        console.log(`✓ Method: ${methodCall.method}`);
        console.log(`  Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
        
        if (response.data && response.data.data !== null && response.data.data !== undefined) {
          console.log(`  🎯 SUCCESS - Got data!`);
          console.log(`  Full response: ${JSON.stringify(response.data, null, 2)}`);
        }
        
        const dataStr = JSON.stringify(response.data).toLowerCase();
        if (dataStr.includes('mac') || dataStr.includes('ip') || dataStr.includes('client') || 
            dataStr.includes('device') || dataStr.includes('dhcp') || dataStr.includes('192.168')) {
          console.log(`  🔥 CONTAINS DEVICE DATA!`);
        }
        console.log('');
        
      } catch (error) {
        // Skip errors for this batch test
      }
    }

  } catch (error) {
    console.error('❌ devSta method test failed:', error.message);
  }
}

testDevStaMethods().catch(console.error);
